#pragma once

#include "CoreMinimal.h"
#include "Templates/Tuple.h"
#include "Engine/Blueprint.h"
#include "K2Node.h"
#include "EdGraph/EdGraph.h"
#include "BlueprintGeneration/NodeFactory.h"
#include "BlueprintGeneration/BlueprintGenerationTypes.h"

DECLARE_LOG_CATEGORY_EXTERN(LogNodePositionCalculator, Log, All);

/**
 * Enumeration for node layout algorithms
 */
UENUM(BlueprintType)
enum class ENodeLayoutAlgorithm : uint8
{
    Linear          UMETA(DisplayName = "Linear Layout"),
    Grid            UMETA(DisplayName = "Grid Layout"),
    Hierarchical    UMETA(DisplayName = "Hierarchical Layout"),
    Force           UMETA(DisplayName = "Force-Directed Layout"),
    Circular        UMETA(DisplayName = "Circular Layout"),
    Tree            UMETA(DisplayName = "Tree Layout"),
    Layered         UMETA(DisplayName = "Layered Layout"),
    Organic         UMETA(DisplayName = "Organic Layout"),
    Custom          UMETA(DisplayName = "Custom Layout")
};

/**
 * Enumeration for node alignment options
 */
UENUM(BlueprintType)
enum class ENodeAlignment : uint8
{
    None            UMETA(DisplayName = "No Alignment"),
    Left            UMETA(DisplayName = "Left Align"),
    Center          UMETA(DisplayName = "Center Align"),
    Right           UMETA(DisplayName = "Right Align"),
    Top             UMETA(DisplayName = "Top Align"),
    Middle          UMETA(DisplayName = "Middle Align"),
    Bottom          UMETA(DisplayName = "Bottom Align"),
    Grid            UMETA(DisplayName = "Grid Align"),
    Distribute      UMETA(DisplayName = "Distribute Evenly")
};

/**
 * Enumeration for node spacing modes
 */
UENUM(BlueprintType)
enum class ENodeSpacingMode : uint8
{
    Fixed           UMETA(DisplayName = "Fixed Spacing"),
    Proportional    UMETA(DisplayName = "Proportional Spacing"),
    Adaptive        UMETA(DisplayName = "Adaptive Spacing"),
    Minimum         UMETA(DisplayName = "Minimum Spacing"),
    Optimal         UMETA(DisplayName = "Optimal Spacing"),
    Compact         UMETA(DisplayName = "Compact Spacing"),
    Loose           UMETA(DisplayName = "Loose Spacing")
};

/**
 * Structure containing node positioning information
 */
USTRUCT(BlueprintType)
struct FNODEPOSITIONINFO
{
    GENERATED_BODY()

    /** The node this position info is for */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position")
    UK2Node* Node;

    /** Current position of the node */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position")
    FVector2D Position;

    /** Desired position of the node */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position")
    FVector2D DesiredPosition;

    /** Size of the node */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position")
    FVector2D Size;

    /** Priority for positioning (higher = more important) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position")
    int32 Priority;

    /** Whether this node is fixed and cannot be moved */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position")
    bool bIsFixed;

    /** Whether this node is selected */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position")
    bool bIsSelected;

    /** Layer/depth of the node in the graph */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position")
    int32 Layer;

    /** Group ID for nodes that should be positioned together */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position")
    int32 GroupID;

    /** Connected nodes (for layout algorithms) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position")
    TArray<UK2Node*> ConnectedNodes;

    /** Custom positioning data */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position")
    TMap<FString, FString> CustomData;

    FNODEPOSITIONINFO()
        : Node(nullptr)
        , Position(FVector2D::ZeroVector)
        , DesiredPosition(FVector2D::ZeroVector)
        , Size(FVector2D(200.0f, 100.0f))
        , Priority(0)
        , bIsFixed(false)
        , bIsSelected(false)
        , Layer(0)
        , GroupID(-1)
    {
    }
};

/**
 * Structure for layout configuration
 */
USTRUCT(BlueprintType)
struct FLAYOUTCONFIGURATION
{
    GENERATED_BODY()

    /** Layout algorithm to use */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    ENodeLayoutAlgorithm Algorithm;

    /** Node alignment mode */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    ENodeAlignment Alignment;

    /** Node spacing mode */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    ENodeSpacingMode SpacingMode;

    /** Horizontal spacing between nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    float HorizontalSpacing;

    /** Vertical spacing between nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    float VerticalSpacing;

    /** Minimum spacing between nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    float MinimumSpacing;

    /** Maximum spacing between nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    float MaximumSpacing;

    /** Padding around the entire layout */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    FVector2D LayoutPadding;

    /** Whether to avoid overlapping nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    bool bAvoidOverlaps;

    /** Whether to maintain aspect ratio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    bool bMaintainAspectRatio;

    /** Whether to animate position changes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    bool bAnimateChanges;

    /** Animation duration in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    float AnimationDuration;

    /** Whether to respect fixed nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    bool bRespectFixedNodes;

    /** Whether to group connected nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    bool bGroupConnectedNodes;

    /** Custom layout parameters */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout")
    TMap<FString, float> CustomParameters;

    FLAYOUTCONFIGURATION()
        : Algorithm(ENodeLayoutAlgorithm::Hierarchical)
        , Alignment(ENodeAlignment::Left)
        , SpacingMode(ENodeSpacingMode::Optimal)
        , HorizontalSpacing(400.0f)
        , VerticalSpacing(300.0f)
        , MinimumSpacing(150.0f)
        , MaximumSpacing(800.0f)
        , LayoutPadding(FVector2D(50.0f, 50.0f))
        , bAvoidOverlaps(true)
        , bMaintainAspectRatio(false)
        , bAnimateChanges(false)
        , AnimationDuration(0.5f)
        , bRespectFixedNodes(true)
        , bGroupConnectedNodes(true)
    {
    }
};

/**
 * Structure for layout calculation results
 */
USTRUCT(BlueprintType)
struct FLAYOUTCALCULATIONRESULT
{
    GENERATED_BODY()

    /** Whether the layout calculation was successful */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    bool bSuccess;

    /** Calculated positions for all nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    TArray<FNODEPOSITIONINFO> NodePositions;

    /** Total bounds of the layout */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    FVector2D LayoutBounds;

    /** Center point of the layout */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    FVector2D LayoutCenter;

    /** Number of iterations performed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    int32 IterationsPerformed;

    /** Calculation time in milliseconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    float CalculationTimeMs;

    /** Quality score of the layout (0.0-1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    float QualityScore;

    /** Number of overlapping nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    int32 OverlapCount;

    /** Error messages if calculation failed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    TArray<FString> ErrorMessages;

    /** Warning messages */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    TArray<FString> WarningMessages;

    /** Performance metrics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    TMap<FString, float> PerformanceMetrics;

    FLAYOUTCALCULATIONRESULT()
        : bSuccess(false)
        , LayoutBounds(FVector2D::ZeroVector)
        , LayoutCenter(FVector2D::ZeroVector)
        , IterationsPerformed(0)
        , CalculationTimeMs(0.0f)
        , QualityScore(0.0f)
        , OverlapCount(0)
    {
    }
};

/**
 * Structure for collision detection results
 */
USTRUCT(BlueprintType)
struct FCOLLISIONDETECTIONRESULT
{
    GENERATED_BODY()

    /** Whether any collisions were detected */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    bool bHasCollisions;

    /** Pairs of colliding nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    TArray<TPair<UK2Node*, UK2Node*>> CollidingPairs;

    /** Collision resolution suggestions */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    TArray<FVector2D> ResolutionSuggestions;

    /** Collision detection time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    float DetectionTimeMs;

    FCOLLISIONDETECTIONRESULT()
        : bHasCollisions(false)
        , DetectionTimeMs(0.0f)
    {
    }
};

/**
 * Class responsible for calculating optimal node positions in blueprint graphs
 */
class UE5BLUEPRINTGENERATOR_API FNodePositionCalculator
{
public:
    FNodePositionCalculator();
    ~FNodePositionCalculator();

    // Initialization and management

    /**
     * Initializes the position calculator
     * @return True if initialization was successful
     */
    bool Initialize();

    /**
     * Shuts down the position calculator
     */
    void Shutdown();

    // Layout calculation

    /**
     * Calculates optimal positions for all nodes in a graph
     * @param Graph - The graph to calculate positions for
     * @param Configuration - Layout configuration
     * @return Layout calculation result
     */
    FLAYOUTCALCULATIONRESULT CalculateLayout(UEdGraph* Graph, const FLAYOUTCONFIGURATION& Configuration);

    /**
     * Calculates positions for a specific set of nodes
     * @param Nodes - The nodes to calculate positions for
     * @param Configuration - Layout configuration
     * @return Layout calculation result
     */
    FLAYOUTCALCULATIONRESULT CalculateLayoutForNodes(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration);

    /**
     * Recalculates layout after adding new nodes
     * @param Graph - The graph to recalculate
     * @param NewNodes - The newly added nodes
     * @param Configuration - Layout configuration
     * @return Layout calculation result
     */
    FLAYOUTCALCULATIONRESULT RecalculateLayoutWithNewNodes(UEdGraph* Graph, const TArray<UK2Node*>& NewNodes, const FLAYOUTCONFIGURATION& Configuration);

    // Specific layout algorithms

    /**
     * Applies linear layout algorithm
     * @param Nodes - Nodes to layout
     * @param Configuration - Layout configuration
     * @return Layout result
     */
    FLAYOUTCALCULATIONRESULT ApplyLinearLayout(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration);

    /**
     * Applies grid layout algorithm
     * @param Nodes - Nodes to layout
     * @param Configuration - Layout configuration
     * @return Layout result
     */
    FLAYOUTCALCULATIONRESULT ApplyGridLayout(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration);

    /**
     * Applies hierarchical layout algorithm
     * @param Nodes - Nodes to layout
     * @param Configuration - Layout configuration
     * @return Layout result
     */
    FLAYOUTCALCULATIONRESULT ApplyHierarchicalLayout(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration);

    /**
     * Applies force-directed layout algorithm
     * @param Nodes - Nodes to layout
     * @param Configuration - Layout configuration
     * @return Layout result
     */
    FLAYOUTCALCULATIONRESULT ApplyForceDirectedLayout(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration);

    /**
     * Applies tree layout algorithm
     * @param Nodes - Nodes to layout
     * @param Configuration - Layout configuration
     * @return Layout result
     */
    FLAYOUTCALCULATIONRESULT ApplyTreeLayout(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration);

    // Position utilities

    /**
     * Gets the optimal position for a new node
     * @param Graph - The graph to add the node to
     * @param NodeType - The type of node being added
     * @param ReferenceNode - Optional reference node for relative positioning
     * @return Optimal position for the new node
     */
    FVector2D GetOptimalPositionForNewNode(UEdGraph* Graph, const FString& NodeType, UK2Node* ReferenceNode = nullptr);

    /**
     * Calculates the next available position in a sequence
     * @param Graph - The graph to calculate for
     * @param Direction - Direction to place the next node (right, down, etc.)
     * @return Next available position
     */
    FVector2D GetNextSequentialPosition(UEdGraph* Graph, const FVector2D& Direction = FVector2D(1.0f, 0.0f));

    /**
     * Finds the best position to minimize connections
     * @param Node - The node to position
     * @param ConnectedNodes - Nodes that this node connects to
     * @return Optimal position to minimize connection lengths
     */
    FVector2D FindOptimalConnectionPosition(UK2Node* Node, const TArray<UK2Node*>& ConnectedNodes);

    // Collision detection and resolution

    /**
     * Detects collisions between nodes
     * @param Nodes - Nodes to check for collisions
     * @return Collision detection result
     */
    FCOLLISIONDETECTIONRESULT DetectCollisions(const TArray<UK2Node*>& Nodes);

    /**
     * Resolves overlapping nodes
     * @param Nodes - Nodes to resolve overlaps for
     * @param Configuration - Layout configuration
     * @return True if overlaps were resolved
     */
    bool ResolveOverlaps(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration);

    /**
     * Checks if two nodes overlap
     * @param Node1 - First node
     * @param Node2 - Second node
     * @param Margin - Additional margin to consider
     * @return True if nodes overlap
     */
    bool DoNodesOverlap(UK2Node* Node1, UK2Node* Node2, float Margin = 0.0f);

    // Node analysis and grouping

    /**
     * Analyzes node connections and dependencies
     * @param Graph - The graph to analyze
     * @return Array of node position info with connection data
     */
    TArray<FNODEPOSITIONINFO> AnalyzeNodeConnections(UEdGraph* Graph);

    /**
     * Groups nodes by their connections
     * @param Nodes - Nodes to group
     * @return Map of group ID to nodes in that group
     */
    TMap<int32, TArray<UK2Node*>> GroupNodesByConnections(const TArray<UK2Node*>& Nodes);

    /**
     * Calculates the depth/layer of each node
     * @param Nodes - Nodes to analyze
     * @param StartNode - Starting node for depth calculation
     * @return Map of node to depth level
     */
    TMap<UK2Node*, int32> CalculateNodeDepths(const TArray<UK2Node*>& Nodes, UK2Node* StartNode = nullptr);

    // Alignment and spacing

    /**
     * Aligns nodes according to the specified alignment
     * @param Nodes - Nodes to align
     * @param Alignment - Alignment mode
     * @return True if alignment was successful
     */
    bool AlignNodes(const TArray<UK2Node*>& Nodes, ENodeAlignment Alignment);

    /**
     * Distributes nodes evenly in the specified direction
     * @param Nodes - Nodes to distribute
     * @param Direction - Direction to distribute (horizontal/vertical)
     * @param Spacing - Spacing between nodes
     * @return True if distribution was successful
     */
    bool DistributeNodes(const TArray<UK2Node*>& Nodes, const FVector2D& Direction, float Spacing);

    /**
     * Calculates optimal spacing between nodes
     * @param Nodes - Nodes to calculate spacing for
     * @param SpacingMode - Spacing calculation mode
     * @return Optimal spacing value
     */
    float CalculateOptimalSpacing(const TArray<UK2Node*>& Nodes, ENodeSpacingMode SpacingMode);

    // Layout quality assessment

    /**
     * Evaluates the quality of a layout
     * @param Nodes - Nodes in the layout
     * @param Configuration - Layout configuration used
     * @return Quality score (0.0-1.0, higher is better)
     */
    float EvaluateLayoutQuality(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration);

    /**
     * Calculates layout metrics
     * @param Nodes - Nodes in the layout
     * @return Map of metric names to values
     */
    TMap<FString, float> CalculateLayoutMetrics(const TArray<UK2Node*>& Nodes);

    /**
     * Suggests layout improvements
     * @param Nodes - Nodes in the layout
     * @param Configuration - Current layout configuration
     * @return Array of improvement suggestions
     */
    TArray<FString> SuggestLayoutImprovements(const TArray<UK2Node*>& Nodes, const FLAYOUTCONFIGURATION& Configuration);

    // Configuration and presets

    /**
     * Gets default layout configuration for a graph type
     * @param GraphType - Type of graph (EventGraph, FunctionGraph, etc.)
     * @return Default configuration for the graph type
     */
    FLAYOUTCONFIGURATION GetDefaultConfiguration(const FString& GraphType);

    /**
     * Saves a layout configuration as a preset
     * @param PresetName - Name of the preset
     * @param Configuration - Configuration to save
     * @return True if preset was saved successfully
     */
    bool SaveLayoutPreset(const FString& PresetName, const FLAYOUTCONFIGURATION& Configuration);

    /**
     * Loads a layout configuration preset
     * @param PresetName - Name of the preset to load
     * @return Loaded configuration, or default if not found
     */
    FLAYOUTCONFIGURATION LoadLayoutPreset(const FString& PresetName);

    /**
     * Gets all available layout presets
     * @return Array of preset names
     */
    TArray<FString> GetAvailablePresets();

    // Utility methods

    /**
     * Gets the size of a node
     * @param Node - Node to get size for
     * @return Size of the node
     */
    FVector2D GetNodeSize(UK2Node* Node);

    /**
     * Gets the bounds of a collection of nodes
     * @param Nodes - Nodes to calculate bounds for
     * @return Bounding rectangle of all nodes
     */
    FVector4 GetNodesBounds(const TArray<UK2Node*>& Nodes);

    /**
     * Converts layout algorithm enum to string
     * @param Algorithm - Algorithm to convert
     * @return String representation
     */
    FString LayoutAlgorithmToString(ENodeLayoutAlgorithm Algorithm);

    /**
     * Converts string to layout algorithm enum
     * @param AlgorithmString - String to convert
     * @return Corresponding algorithm enum
     */
    ENodeLayoutAlgorithm StringToLayoutAlgorithm(const FString& AlgorithmString);

    /**
     * Gets position calculator statistics
     * @return Map of statistics
     */
    TMap<FString, FString> GetPositionCalculatorStatistics();

private:
    // Layout algorithm implementations

    /**
     * Implements linear layout logic
     * @param NodePositions - Node positions to modify
     * @param Configuration - Layout configuration
     */
    void ImplementLinearLayout(TArray<FNODEPOSITIONINFO>& NodePositions, const FLAYOUTCONFIGURATION& Configuration);

    /**
     * Implements grid layout logic
     * @param NodePositions - Node positions to modify
     * @param Configuration - Layout configuration
     */
    void ImplementGridLayout(TArray<FNODEPOSITIONINFO>& NodePositions, const FLAYOUTCONFIGURATION& Configuration);

    /**
     * Implements hierarchical layout logic
     * @param NodePositions - Node positions to modify
     * @param Configuration - Layout configuration
     */
    void ImplementHierarchicalLayout(TArray<FNODEPOSITIONINFO>& NodePositions, const FLAYOUTCONFIGURATION& Configuration);

    /**
     * Implements force-directed layout logic
     * @param NodePositions - Node positions to modify
     * @param Configuration - Layout configuration
     * @param MaxIterations - Maximum number of iterations
     */
    void ImplementForceDirectedLayout(TArray<FNODEPOSITIONINFO>& NodePositions, const FLAYOUTCONFIGURATION& Configuration, int32 MaxIterations = 100);

    // Helper methods

    /**
     * Calculates repulsive force between nodes
     * @param Node1 - First node
     * @param Node2 - Second node
     * @return Repulsive force vector
     */
    FVector2D CalculateRepulsiveForce(const FNODEPOSITIONINFO& Node1, const FNODEPOSITIONINFO& Node2);

    /**
     * Calculates attractive force between connected nodes
     * @param Node1 - First node
     * @param Node2 - Second node
     * @return Attractive force vector
     */
    FVector2D CalculateAttractiveForce(const FNODEPOSITIONINFO& Node1, const FNODEPOSITIONINFO& Node2);

    /**
     * Applies forces to node positions
     * @param NodePositions - Node positions to modify
     * @param Forces - Forces to apply to each node
     * @param DampingFactor - Damping factor for stability
     */
    void ApplyForces(TArray<FNODEPOSITIONINFO>& NodePositions, const TArray<FVector2D>& Forces, float DampingFactor = 0.9f);

    /**
     * Builds connection matrix for nodes
     * @param Nodes - Nodes to build matrix for
     * @return 2D array representing connections
     */
    TArray<TArray<bool>> BuildConnectionMatrix(const TArray<UK2Node*>& Nodes);

    /**
     * Finds root nodes (nodes with no inputs)
     * @param Nodes - Nodes to search
     * @return Array of root nodes
     */
    TArray<UK2Node*> FindRootNodes(const TArray<UK2Node*>& Nodes);

    /**
     * Performs topological sort on nodes
     * @param Nodes - Nodes to sort
     * @return Topologically sorted nodes
     */
    TArray<UK2Node*> TopologicalSort(const TArray<UK2Node*>& Nodes);

    // Quality assessment helpers

    /**
     * Calculates connection length penalty
     * @param Nodes - Nodes to evaluate
     * @return Penalty score for long connections
     */
    float CalculateConnectionLengthPenalty(const TArray<UK2Node*>& Nodes);

    /**
     * Calculates overlap penalty
     * @param Nodes - Nodes to evaluate
     * @return Penalty score for overlapping nodes
     */
    float CalculateOverlapPenalty(const TArray<UK2Node*>& Nodes);

    /**
     * Calculates alignment score
     * @param Nodes - Nodes to evaluate
     * @return Score for node alignment quality
     */
    float CalculateAlignmentScore(const TArray<UK2Node*>& Nodes);

    // Logging and statistics

    /**
     * Logs position calculator activity
     * @param Message - Message to log
     * @param bIsError - Whether this is an error message
     */
    void LogPositionActivity(const FString& Message, bool bIsError = false);

    /**
     * Updates position calculator statistics
     * @param Operation - Operation that was performed
     * @param NodeCount - Number of nodes involved
     */
    void UpdatePositionStatistics(const FString& Operation, int32 NodeCount = 0);

    // Member variables

    /** Whether the position calculator has been initialized */
    bool bIsInitialized;

    /** Whether to enable verbose logging */
    bool bVerboseLogging;

    /** Whether to track position statistics */
    bool bTrackPositionStatistics;

    /** Default layout configurations for different graph types */
    TMap<FString, FLAYOUTCONFIGURATION> DefaultConfigurations;

    /** Saved layout presets */
    TMap<FString, FLAYOUTCONFIGURATION> LayoutPresets;

    /** Position calculator statistics */
    TMap<FString, int32> PositionStatistics;

    /** Performance metrics for different algorithms */
    TMap<ENodeLayoutAlgorithm, float> AlgorithmPerformance;

    /** Total number of layout calculations performed */
    int32 TotalLayoutCalculations;

    /** Total number of collision detections performed */
    int32 TotalCollisionDetections;

    /** Average calculation time per layout */
    float AverageCalculationTime;
}; 