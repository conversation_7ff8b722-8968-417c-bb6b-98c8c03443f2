#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "K2Node.h"
#include "BlueprintGeneration/BlueprintGenerationTypes.h"
#include "KismetCompiler.h"
#include "BlueprintGeneration/AssemblyCoordinator.h"

DECLARE_LOG_CATEGORY_EXTERN(LogCompilationManager, Log, All);

/**
 * Compilation strategy enumeration
 */
UENUM(BlueprintType)
enum class ECompilationStrategy : uint8
{
    Standard,           // Standard UE5 compilation
    Optimized,          // Optimized compilation with performance focus
    Debug,              // Debug compilation with extra validation
    Fast,               // Fast compilation for iteration
    Thorough,           // Thorough compilation with comprehensive checks
    Custom              // Custom compilation strategy
};

/**
 * Compilation mode enumeration
 */
UENUM(BlueprintType)
enum class ECompilationMode : uint8
{
    Full,               // Full blueprint compilation
    Incremental,        // Incremental compilation of changes only
    Validation,         // Validation-only compilation
    Skeleton,           // Skeleton-only compilation
    Dependencies        // Dependencies-only compilation
};

/**
 * Compilation priority enumeration
 */
UENUM(BlueprintType)
enum class ECompilationPriority : uint8
{
    Critical,           // Critical priority compilation
    High,               // High priority compilation
    Normal,             // Normal priority compilation
    Low,                // Low priority compilation
    Background          // Background compilation
};

/**
 * Compilation status enumeration
 */
UENUM(BlueprintType)
enum class ECompilationStatus : uint8
{
    NotStarted,         // Compilation not started
    InProgress,         // Compilation in progress
    Succeeded,          // Compilation succeeded
    Failed,             // Compilation failed
    Warning,            // Compilation succeeded with warnings
    Cancelled           // Compilation was cancelled
};

/**
 * Compilation error severity enumeration
 */
UENUM(BlueprintType)
enum class ECompilationErrorSeverity : uint8
{
    Info,               // Informational message
    Warning,            // Warning message
    Error,              // Error message
    Fatal               // Fatal error message
};

/**
 * Compilation request structure
 */
USTRUCT(BlueprintType)
struct FCOMPILATIONREQUEST
{
    GENERATED_BODY()

    // Request identifier
    UPROPERTY(BlueprintReadOnly, Category = "Request")
    FString RequestId;

    // Target blueprint
    UPROPERTY(BlueprintReadOnly, Category = "Request")
    TObjectPtr<UBlueprint> Blueprint = nullptr;

    // Compilation strategy
    UPROPERTY(BlueprintReadOnly, Category = "Request")
    ECompilationStrategy Strategy = ECompilationStrategy::Standard;

    // Compilation mode
    UPROPERTY(BlueprintReadOnly, Category = "Request")
    ECompilationMode Mode = ECompilationMode::Full;

    // Compilation priority
    UPROPERTY(BlueprintReadOnly, Category = "Request")
    ECompilationPriority Priority = ECompilationPriority::Normal;

    // Force recompilation
    UPROPERTY(BlueprintReadOnly, Category = "Request")
    bool bForceRecompilation = false;

    // Skip validation
    UPROPERTY(BlueprintReadOnly, Category = "Request")
    bool bSkipValidation = false;

    // Enable optimization
    UPROPERTY(BlueprintReadOnly, Category = "Request")
    bool bEnableOptimization = true;

    // Generate debug information
    UPROPERTY(BlueprintReadOnly, Category = "Request")
    bool bGenerateDebugInfo = false;

    // Compilation timeout (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Request")
    float CompilationTimeout = 60.0f;

    // Custom compilation options
    UPROPERTY(BlueprintReadOnly, Category = "Request")
    TMap<FString, FString> CustomOptions;

    // Request timestamp
    UPROPERTY(BlueprintReadOnly, Category = "Request")
    FDateTime RequestTime;

    FCOMPILATIONREQUEST()
    {
        Strategy = ECompilationStrategy::Standard;
        Mode = ECompilationMode::Full;
        Priority = ECompilationPriority::Normal;
        bForceRecompilation = false;
        bSkipValidation = false;
        bEnableOptimization = true;
        bGenerateDebugInfo = false;
        CompilationTimeout = 60.0f;
        RequestTime = FDateTime::Now();
    }
};

/**
 * Compilation error structure
 */
USTRUCT(BlueprintType)
struct FCOMPILATIONERROR
{
    GENERATED_BODY()

    // Error identifier
    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString ErrorId;

    // Error severity
    UPROPERTY(BlueprintReadOnly, Category = "Error")
    ECompilationErrorSeverity Severity = ECompilationErrorSeverity::Error;

    // Error message
    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString Message;

    // Error details
    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString Details;

    // Source node (if applicable)
    UPROPERTY(BlueprintReadOnly, Category = "Error")
    TObjectPtr<UK2Node> SourceNode = nullptr;

    // Source graph (if applicable)
    UPROPERTY(BlueprintReadOnly, Category = "Error")
    TObjectPtr<UEdGraph> SourceGraph = nullptr;

    // Error location
    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString Location;

    // Suggested fix
    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString SuggestedFix;

    // Can auto-fix
    UPROPERTY(BlueprintReadOnly, Category = "Error")
    bool bCanAutoFix = false;

    // Error timestamp
    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FDateTime ErrorTime;

    FCOMPILATIONERROR()
    {
        Severity = ECompilationErrorSeverity::Error;
        bCanAutoFix = false;
        ErrorTime = FDateTime::Now();
    }
};

/**
 * Compilation result structure
 */
USTRUCT(BlueprintType)
struct FCOMPILATIONRESULT
{
    GENERATED_BODY()

    // Request identifier
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString RequestId;

    // Compilation status
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    ECompilationStatus Status = ECompilationStatus::NotStarted;

    // Compilation success
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    // Compilation time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float CompilationTime = 0.0f;

    // Generated class
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TObjectPtr<UClass> GeneratedClass = nullptr;

    // Compilation errors
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FCompilationError> Errors;

    // Compilation warnings
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FCompilationError> Warnings;

    // Compilation info messages
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FCompilationError> InfoMessages;

    // Optimization applied
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bOptimizationApplied = false;

    // Optimization details
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString OptimizationDetails;

    // Debug information generated
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bDebugInfoGenerated = false;

    // Compilation statistics
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TMap<FString, FString> Statistics;

    // Result timestamp
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FDateTime ResultTime;

    FCOMPILATIONRESULT()
    {
        Status = ECompilationStatus::NotStarted;
        bSuccess = false;
        CompilationTime = 0.0f;
        bOptimizationApplied = false;
        bDebugInfoGenerated = false;
        ResultTime = FDateTime::Now();
    }
};

/**
 * Compilation configuration structure
 */
USTRUCT(BlueprintType)
struct FCOMPILATIONCONFIG
{
    GENERATED_BODY()

    // Enable parallel compilation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compilation")
    bool bEnableParallelCompilation = true;

    // Enable compilation caching
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compilation")
    bool bEnableCompilationCaching = true;

    // Enable incremental compilation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compilation")
    bool bEnableIncrementalCompilation = true;

    // Enable error recovery
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compilation")
    bool bEnableErrorRecovery = true;

    // Enable auto-fix
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compilation")
    bool bEnableAutoFix = true;

    // Enable performance monitoring
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compilation")
    bool bEnablePerformanceMonitoring = true;

    // Enable statistics tracking
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compilation")
    bool bEnableStatistics = true;

    // Default compilation strategy
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compilation")
    ECompilationStrategy DefaultStrategy = ECompilationStrategy::Standard;

    // Default compilation mode
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compilation")
    ECompilationMode DefaultMode = ECompilationMode::Full;

    // Default compilation timeout (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "600"))
    float DefaultTimeout = 60.0f;

    // Maximum parallel compilations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "16"))
    int32 MaxParallelCompilations = 4;

    // Maximum retry attempts
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Recovery", meta = (ClampMin = "0", ClampMax = "10"))
    int32 MaxRetryAttempts = 3;

    // Retry delay (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Recovery", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float RetryDelay = 2.0f;

    // Cache size limit (MB)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Caching", meta = (ClampMin = "10", ClampMax = "1000"))
    int32 CacheSizeLimit = 100;

    // Cache expiration time (hours)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Caching", meta = (ClampMin = "1", ClampMax = "168"))
    int32 CacheExpirationHours = 24;

    FCOMPILATIONCONFIG()
    {
        bEnableParallelCompilation = true;
        bEnableCompilationCaching = true;
        bEnableIncrementalCompilation = true;
        bEnableErrorRecovery = true;
        bEnableAutoFix = true;
        bEnablePerformanceMonitoring = true;
        bEnableStatistics = true;
        DefaultStrategy = ECompilationStrategy::Standard;
        DefaultMode = ECompilationMode::Full;
        DefaultTimeout = 60.0f;
        MaxParallelCompilations = 4;
        MaxRetryAttempts = 3;
        RetryDelay = 2.0f;
        CacheSizeLimit = 100;
        CacheExpirationHours = 24;
    }
};

/**
 * Compilation statistics structure
 */
USTRUCT(BlueprintType)
struct FCOMPILATIONSTATISTICS
{
    GENERATED_BODY()

    // Total compilations
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalCompilations = 0;

    // Successful compilations
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 SuccessfulCompilations = 0;

    // Failed compilations
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 FailedCompilations = 0;

    // Compilations with warnings
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 CompilationsWithWarnings = 0;

    // Cancelled compilations
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 CancelledCompilations = 0;

    // Total compilation time
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float TotalCompilationTime = 0.0f;

    // Average compilation time
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageCompilationTime = 0.0f;

    // Fastest compilation time
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float FastestCompilationTime = 0.0f;

    // Slowest compilation time
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float SlowestCompilationTime = 0.0f;

    // Cache hits
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 CacheHits = 0;

    // Cache misses
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 CacheMisses = 0;

    // Auto-fixes applied
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 AutoFixesApplied = 0;

    // Strategy usage
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<ECompilationStrategy, int32> StrategyUsage;

    // Mode usage
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<ECompilationMode, int32> ModeUsage;

    // Error frequency
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<FString, int32> ErrorFrequency;

    FCOMPILATIONSTATISTICS()
    {
        TotalCompilations = 0;
        SuccessfulCompilations = 0;
        FailedCompilations = 0;
        CompilationsWithWarnings = 0;
        CancelledCompilations = 0;
        TotalCompilationTime = 0.0f;
        AverageCompilationTime = 0.0f;
        FastestCompilationTime = 0.0f;
        SlowestCompilationTime = 0.0f;
        CacheHits = 0;
        CacheMisses = 0;
        AutoFixesApplied = 0;
    }
};

/**
 * Compilation context structure
 */
USTRUCT(BlueprintType)
struct FCOMPILATIONCONTEXT
{
    GENERATED_BODY()

    // Target blueprint
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<UBlueprint> Blueprint = nullptr;

    // Compilation configuration
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FCompilationConfig Config;

    // Compilation start time
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FDateTime CompilationStartTime;

    // Active compiler
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<FKismetCompilerContext> CompilerContext = nullptr;

    // Compilation dependencies
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TArray<TObjectPtr<UBlueprint>> Dependencies;

    // Compilation cache
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TMap<FString, FString> CompilationCache;

    // Error recovery attempts
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    int32 ErrorRecoveryAttempts = 0;

    // Compilation progress
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    float CompilationProgress = 0.0f;

    FCOMPILATIONCONTEXT()
    {
        CompilationStartTime = FDateTime::Now();
        ErrorRecoveryAttempts = 0;
        CompilationProgress = 0.0f;
    }
};

/**
 * Delegate declarations for compilation events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCompilationStarted, const FString&, RequestId);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCompilationProgress, const FString&, RequestId, float, Progress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCompilationCompleted, const FCompilationResult&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCompilationFailed, const FString&, RequestId, const FString&, ErrorMessage);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCompilationError, const FString&, RequestId, const FCompilationError&, Error);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCompilationWarning, const FString&, RequestId, const FCompilationError&, Warning);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAutoFixApplied, const FString&, RequestId, const FString&, FixDescription);

/**
 * Compilation Manager - Comprehensive blueprint compilation orchestration system
 * 
 * This class provides complete compilation management for generated blueprints,
 * including compilation strategies, error handling, optimization, caching, and
 * performance monitoring. It ensures that blueprints are compiled correctly
 * and efficiently with comprehensive error recovery and quality assurance.
 */
class UE5BLUEPRINTGENERATOR_API FCompilationManager
{
public:
    FCompilationManager();
    virtual ~FCompilationManager();

    // Core compilation operations
    bool CompileBlueprint(const FCompilationRequest& Request, FCompilationResult& OutResult);
    bool CompileBlueprints(const TArray<FCompilationRequest>& Requests, TArray<FCompilationResult>& OutResults);
    bool CompileBlueprintAsync(const FCompilationRequest& Request, TFunction<void(const FCompilationResult&)> OnCompleted);

    // Compilation validation
    bool ValidateBlueprint(UBlueprint* Blueprint, TArray<FCompilationError>& OutErrors);
    bool ValidateBlueprintForCompilation(UBlueprint* Blueprint, const FCompilationConfig& Config, TArray<FCompilationError>& OutErrors);

    // Compilation optimization
    bool OptimizeBlueprint(UBlueprint* Blueprint, const FCompilationConfig& Config, FString& OutOptimizationDetails);
    bool ApplyCompilationOptimizations(UBlueprint* Blueprint, ECompilationStrategy Strategy, FString& OutDetails);

    // Error handling and recovery
    bool HandleCompilationError(const FCompilationError& Error, FCompilationContext& Context, bool& OutFixed);
    bool AttemptAutoFix(const FCompilationError& Error, UBlueprint* Blueprint, FString& OutFixDescription);
    bool RecoverFromCompilationFailure(const FCompilationRequest& Request, FCompilationContext& Context, FCompilationResult& OutResult);

    // Compilation caching
    bool IsBlueprintCached(UBlueprint* Blueprint, const FCompilationConfig& Config);
    bool CacheCompilationResult(UBlueprint* Blueprint, const FCompilationConfig& Config, const FCompilationResult& Result);
    bool GetCachedCompilationResult(UBlueprint* Blueprint, const FCompilationConfig& Config, FCompilationResult& OutResult);
    void ClearCompilationCache();

    // Dependency management
    TArray<UBlueprint*> GetCompilationDependencies(UBlueprint* Blueprint);
    bool CompileDependencies(const TArray<UBlueprint*>& Dependencies, TArray<FCompilationResult>& OutResults);
    bool ValidateDependencies(UBlueprint* Blueprint, TArray<FCompilationError>& OutErrors);

    // Configuration management
    void SetCompilationConfig(const FCompilationConfig& Config);
    FCompilationConfig GetCompilationConfig() const;

    // Context management
    FCompilationContext CreateCompilationContext(UBlueprint* Blueprint, const FCompilationConfig& Config);
    bool ValidateCompilationContext(const FCompilationContext& Context);

    // Statistics and monitoring
    FCompilationStatistics GetStatistics() const;
    void ResetStatistics();

    // Strategy management
    void SetDefaultStrategy(ECompilationStrategy Strategy);
    ECompilationStrategy GetDefaultStrategy() const;
    ECompilationStrategy SelectOptimalStrategy(UBlueprint* Blueprint, const FCompilationConfig& Config);

    // Request management
    FString CreateCompilationRequest(UBlueprint* Blueprint, ECompilationStrategy Strategy = ECompilationStrategy::Standard, ECompilationMode Mode = ECompilationMode::Full);
    bool CancelCompilation(const FString& RequestId);
    bool IsCompilationInProgress(const FString& RequestId);

    // Event delegates
    FOnCompilationStarted OnCompilationStarted;
    FOnCompilationProgress OnCompilationProgress;
    FOnCompilationCompleted OnCompilationCompleted;
    FOnCompilationFailed OnCompilationFailed;
    FOnCompilationError OnCompilationError;
    FOnCompilationWarning OnCompilationWarning;
    FOnAutoFixApplied OnAutoFixApplied;

private:
    // Compilation configuration
    FCompilationConfig Config;

    // Compilation statistics
    FCompilationStatistics Statistics;

    // Active compilation contexts
    TMap<FString, FCompilationContext> ActiveCompilations;

    // Compilation cache
    TMap<FString, FCompilationResult> CompilationCache;

    // Performance monitoring
    TMap<FString, FDateTime> CompilationTimers;

    // Core compilation methods
    bool ExecuteCompilation(const FCompilationRequest& Request, FCompilationContext& Context, FCompilationResult& OutResult);
    bool ExecuteStandardCompilation(const FCompilationRequest& Request, FCompilationContext& Context, FCompilationResult& OutResult);
    bool ExecuteOptimizedCompilation(const FCompilationRequest& Request, FCompilationContext& Context, FCompilationResult& OutResult);
    bool ExecuteDebugCompilation(const FCompilationRequest& Request, FCompilationContext& Context, FCompilationResult& OutResult);
    bool ExecuteFastCompilation(const FCompilationRequest& Request, FCompilationContext& Context, FCompilationResult& OutResult);
    bool ExecuteThoroughCompilation(const FCompilationRequest& Request, FCompilationContext& Context, FCompilationResult& OutResult);

    // Mode implementation methods
    bool ExecuteFullCompilation(const FCompilationRequest& Request, FCompilationContext& Context, FCompilationResult& OutResult);
    bool ExecuteIncrementalCompilation(const FCompilationRequest& Request, FCompilationContext& Context, FCompilationResult& OutResult);
    bool ExecuteValidationCompilation(const FCompilationRequest& Request, FCompilationContext& Context, FCompilationResult& OutResult);
    bool ExecuteSkeletonCompilation(const FCompilationRequest& Request, FCompilationContext& Context, FCompilationResult& OutResult);
    bool ExecuteDependenciesCompilation(const FCompilationRequest& Request, FCompilationContext& Context, FCompilationResult& OutResult);

    // Validation methods
    bool ValidateBlueprintStructure(UBlueprint* Blueprint, TArray<FCompilationError>& OutErrors);
    bool ValidateBlueprintNodes(UBlueprint* Blueprint, TArray<FCompilationError>& OutErrors);
    bool ValidateBlueprintConnections(UBlueprint* Blueprint, TArray<FCompilationError>& OutErrors);
    bool ValidateBlueprintProperties(UBlueprint* Blueprint, TArray<FCompilationError>& OutErrors);

    // Optimization methods
    bool OptimizeNodeLayout(UBlueprint* Blueprint, FString& OutDetails);
    bool OptimizeConnections(UBlueprint* Blueprint, FString& OutDetails);
    bool OptimizeVariables(UBlueprint* Blueprint, FString& OutDetails);
    bool OptimizeFunctions(UBlueprint* Blueprint, FString& OutDetails);

    // Error handling methods
    bool AnalyzeCompilationError(const FString& ErrorMessage, FCompilationError& OutError);
    bool ClassifyCompilationError(const FCompilationError& Error);
    bool GenerateErrorSuggestions(const FCompilationError& Error, TArray<FString>& OutSuggestions);

    // Auto-fix methods
    bool FixMissingConnections(UBlueprint* Blueprint, FString& OutDescription);
    bool FixInvalidNodeProperties(UBlueprint* Blueprint, FString& OutDescription);
    bool FixCircularDependencies(UBlueprint* Blueprint, FString& OutDescription);
    bool FixNamingConventions(UBlueprint* Blueprint, FString& OutDescription);

    // Caching methods
    FString GenerateCacheKey(UBlueprint* Blueprint, const FCompilationConfig& Config);
    bool IsCacheValid(const FString& CacheKey);
    void CleanupExpiredCache();

    // Performance monitoring
    void StartCompilationTimer(const FString& RequestId);
    void StopCompilationTimer(const FString& RequestId, FCompilationResult& Result);
    void UpdateStatistics(const FCompilationResult& Result);

    // Event broadcasting
    void BroadcastCompilationStarted(const FString& RequestId);
    void BroadcastCompilationProgress(const FString& RequestId, float Progress);
    void BroadcastCompilationCompleted(const FCompilationResult& Result);
    void BroadcastCompilationFailed(const FString& RequestId, const FString& ErrorMessage);
    void BroadcastCompilationError(const FString& RequestId, const FCompilationError& Error);
    void BroadcastCompilationWarning(const FString& RequestId, const FCompilationError& Warning);
    void BroadcastAutoFixApplied(const FString& RequestId, const FString& FixDescription);

    // Utility methods
    FString GenerateRequestId();
    ECompilationPriority DeterminePriority(UBlueprint* Blueprint, const FCompilationConfig& Config);
    bool ShouldUseCache(const FCompilationRequest& Request);
    bool CanApplyAutoFix(const FCompilationError& Error);

    // Component initialization
    void InitializeComponents();
    void ShutdownComponents();
}; 