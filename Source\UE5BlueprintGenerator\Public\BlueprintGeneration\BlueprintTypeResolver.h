#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "BlueprintGeneration/BlueprintAssetCreator.h"
#include "NLP/BlueprintPatternDetector.h"
#include "NLP/IntentAnalyzer.h"
#include "NLP/StructureExtractor.h"
#include "NLP/TechnicalTermRecognizer.h"

DECLARE_LOG_CATEGORY_EXTERN(LogBlueprintTypeResolver, Log, All);

/**
 * Enumeration for blueprint type resolution confidence levels
 */
UENUM(BlueprintType)
enum class ETypeResolutionConfidence : uint8
{
    VeryLow     UMETA(DisplayName = "Very Low (0-20%)"),
    Low         UMETA(DisplayName = "Low (20-40%)"),
    Medium      UMETA(DisplayName = "Medium (40-60%)"),
    High        UMETA(DisplayName = "High (60-80%)"),
    VeryHigh    UMETA(DisplayName = "Very High (80-100%)")
};

/**
 * Structure containing blueprint type resolution information
 */
USTRUCT(BlueprintType)
struct FBLUEPRINTTYPERESOLUTION
{
    GENERATED_BODY()

    /** Resolved blueprint type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Resolution")
    EBlueprintType ResolvedType;

    /** Confidence level of the resolution */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Resolution")
    ETypeResolutionConfidence ConfidenceLevel;

    /** Numerical confidence score (0.0-1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Resolution")
    float ConfidenceScore;

    /** Resolved parent class */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Resolution")
    UClass* ResolvedParentClass;

    /** Alternative blueprint types with their confidence scores */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Resolution")
    TMap<EBlueprintType, float> AlternativeTypes;

    /** Reasoning for the resolution */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Resolution")
    TArray<FString> ResolutionReasons;

    /** Warnings about the resolution */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Resolution")
    TArray<FString> ResolutionWarnings;

    /** Whether the resolution is compatible with detected patterns */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Resolution")
    bool bIsPatternCompatible;

    /** Whether the resolution is compatible with detected intent */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Resolution")
    bool bIsIntentCompatible;

    FBLUEPRINTTYPERESOLUTION()
        : ResolvedType(EBlueprintType::Actor)
        , ConfidenceLevel(ETypeResolutionConfidence::Medium)
        , ConfidenceScore(0.5f)
        , ResolvedParentClass(nullptr)
        , bIsPatternCompatible(true)
        , bIsIntentCompatible(true)
    {
    }
};

/**
 * Structure containing parent class resolution information
 */
USTRUCT(BlueprintType)
struct FPARENTCLASSRESOLUTION
{
    GENERATED_BODY()

    /** Resolved parent class */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parent Class Resolution")
    UClass* ResolvedClass;

    /** Confidence score for the resolution */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parent Class Resolution")
    float ConfidenceScore;

    /** Alternative parent classes with their confidence scores */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parent Class Resolution")
    TMap<UClass*, float> AlternativeClasses;

    /** Reasoning for the parent class selection */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parent Class Resolution")
    TArray<FString> ResolutionReasons;

    /** Whether the class is compatible with the blueprint type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parent Class Resolution")
    bool bIsTypeCompatible;

    /** Whether the class supports detected features */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parent Class Resolution")
    bool bSupportsDetectedFeatures;

    FPARENTCLASSRESOLUTION()
        : ResolvedClass(nullptr)
        , ConfidenceScore(0.5f)
        , bIsTypeCompatible(true)
        , bSupportsDetectedFeatures(true)
    {
    }
};

/**
 * Structure containing compatibility validation results
 */
USTRUCT(BlueprintType)
struct FCOMPATIBILITYVALIDATION
{
    GENERATED_BODY()

    /** Whether the blueprint type is compatible with detected patterns */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compatibility")
    bool bIsPatternCompatible;

    /** Whether the blueprint type is compatible with detected intent */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compatibility")
    bool bIsIntentCompatible;

    /** Whether the parent class is compatible with the blueprint type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compatibility")
    bool bIsParentClassCompatible;

    /** Whether the blueprint supports detected features */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compatibility")
    bool bSupportsDetectedFeatures;

    /** Compatibility issues found */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compatibility")
    TArray<FString> CompatibilityIssues;

    /** Compatibility warnings */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compatibility")
    TArray<FString> CompatibilityWarnings;

    /** Overall compatibility score (0.0-1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compatibility")
    float OverallCompatibilityScore;

    FCOMPATIBILITYVALIDATION()
        : bIsPatternCompatible(true)
        , bIsIntentCompatible(true)
        , bIsParentClassCompatible(true)
        , bSupportsDetectedFeatures(true)
        , OverallCompatibilityScore(1.0f)
    {
    }
};

/**
 * Class responsible for resolving blueprint types based on NLP analysis
 */
class UE5BLUEPRINTGENERATOR_API FBlueprintTypeResolver
{
public:
    FBlueprintTypeResolver();
    ~FBlueprintTypeResolver();

    // Type resolution methods

    /**
     * Resolves the most appropriate blueprint type based on NLP analysis
     * @param PatternType - Detected blueprint pattern
     * @param Intent - Detected user intent
     * @param Structure - Extracted blueprint structure
     * @param TechnicalTerms - Recognized technical terms
     * @return Blueprint type resolution result
     */
    FBLUEPRINTTYPERESOLUTION ResolveType(
        EBlueprintPatternType PatternType,
        const FIntentAnalysisResult& Intent,
        const FExtractedBlueprintStructure& Structure,
        const TArray<FTechnicalTerm>& TechnicalTerms
    );

    /**
     * Resolves blueprint type from pattern analysis only
     * @param PatternType - Detected blueprint pattern
     * @param PatternConfidence - Confidence of pattern detection
     * @return Blueprint type resolution result
     */
    FBLUEPRINTTYPERESOLUTION ResolveTypeFromPattern(EBlueprintPatternType PatternType, float PatternConfidence);

    /**
     * Resolves blueprint type from intent analysis only
     * @param Intent - Detected user intent
     * @return Blueprint type resolution result
     */
    FBLUEPRINTTYPERESOLUTION ResolveTypeFromIntent(const FIntentAnalysisResult& Intent);

    /**
     * Resolves blueprint type from structure analysis only
     * @param Structure - Extracted blueprint structure
     * @return Blueprint type resolution result
     */
    FBLUEPRINTTYPERESOLUTION ResolveTypeFromStructure(const FExtractedBlueprintStructure& Structure);

    // Parent class resolution methods

    /**
     * Resolves the most appropriate parent class for a blueprint type
     * @param BlueprintType - The blueprint type to resolve parent class for
     * @param Intent - Detected user intent
     * @param Structure - Extracted blueprint structure
     * @param TechnicalTerms - Recognized technical terms
     * @return Parent class resolution result
     */
    FPARENTCLASSRESOLUTION ResolveParentClass(
        EBlueprintType BlueprintType,
        const FIntentAnalysisResult& Intent,
        const FExtractedBlueprintStructure& Structure,
        const TArray<FTechnicalTerm>& TechnicalTerms
    );

    /**
     * Resolves parent class based on detected components
     * @param BlueprintType - The blueprint type
     * @param Components - Detected components in the structure
     * @return Parent class resolution result
     */
    FPARENTCLASSRESOLUTION ResolveParentClassFromComponents(
        EBlueprintType BlueprintType,
        const TArray<FExtractedComponent>& Components
    );

    /**
     * Resolves parent class based on detected functionality
     * @param BlueprintType - The blueprint type
     * @param Functions - Detected functions in the structure
     * @param Events - Detected events in the structure
     * @return Parent class resolution result
     */
    FPARENTCLASSRESOLUTION ResolveParentClassFromFunctionality(
        EBlueprintType BlueprintType,
        const TArray<FExtractedFunction>& Functions,
        const TArray<FExtractedEvent>& Events
    );

    // Compatibility validation methods

    /**
     * Validates compatibility between blueprint type and analysis results
     * @param BlueprintType - The blueprint type to validate
     * @param PatternType - Detected blueprint pattern
     * @param Intent - Detected user intent
     * @param Structure - Extracted blueprint structure
     * @return Compatibility validation result
     */
    FCOMPATIBILITYVALIDATION ValidateCompatibility(
        EBlueprintType BlueprintType,
        EBlueprintPatternType PatternType,
        const FIntentAnalysisResult& Intent,
        const FExtractedBlueprintStructure& Structure
    );

    /**
     * Validates pattern compatibility with blueprint type
     * @param BlueprintType - The blueprint type
     * @param PatternType - The pattern type to validate
     * @return True if compatible
     */
    bool IsPatternCompatible(EBlueprintType BlueprintType, EBlueprintPatternType PatternType);

    /**
     * Validates intent compatibility with blueprint type
     * @param BlueprintType - The blueprint type
     * @param Intent - The intent to validate
     * @return True if compatible
     */
    bool IsIntentCompatible(EBlueprintType BlueprintType, const FIntentAnalysisResult& Intent);

    /**
     * Validates parent class compatibility with blueprint type
     * @param BlueprintType - The blueprint type
     * @param ParentClass - The parent class to validate
     * @return True if compatible
     */
    bool IsParentClassCompatible(EBlueprintType BlueprintType, UClass* ParentClass);

    // Configuration and customization methods

    /**
     * Sets custom type resolution rules
     * @param PatternType - Pattern type to set rule for
     * @param BlueprintType - Blueprint type to resolve to
     * @param ConfidenceBoost - Additional confidence boost for this rule
     */
    void SetCustomTypeRule(EBlueprintPatternType PatternType, EBlueprintType BlueprintType, float ConfidenceBoost = 0.2f);

    /**
     * Sets custom parent class resolution rules
     * @param BlueprintType - Blueprint type to set rule for
     * @param ParentClass - Parent class to resolve to
     * @param ConfidenceBoost - Additional confidence boost for this rule
     */
    void SetCustomParentClassRule(EBlueprintType BlueprintType, UClass* ParentClass, float ConfidenceBoost = 0.2f);

    /**
     * Sets confidence thresholds for resolution levels
     * @param VeryLowThreshold - Threshold for very low confidence (default: 0.2)
     * @param LowThreshold - Threshold for low confidence (default: 0.4)
     * @param MediumThreshold - Threshold for medium confidence (default: 0.6)
     * @param HighThreshold - Threshold for high confidence (default: 0.8)
     */
    void SetConfidenceThresholds(float VeryLowThreshold, float LowThreshold, float MediumThreshold, float HighThreshold);

    // Utility methods

    /**
     * Gets all supported blueprint types
     * @return Array of all supported blueprint types
     */
    TArray<EBlueprintType> GetSupportedBlueprintTypes();

    /**
     * Gets default parent class for a blueprint type
     * @param BlueprintType - The blueprint type
     * @return Default parent class for the type
     */
    UClass* GetDefaultParentClass(EBlueprintType BlueprintType);

    /**
     * Gets all compatible parent classes for a blueprint type
     * @param BlueprintType - The blueprint type
     * @return Array of compatible parent classes
     */
    TArray<UClass*> GetCompatibleParentClasses(EBlueprintType BlueprintType);

    /**
     * Converts confidence score to confidence level enum
     * @param ConfidenceScore - Numerical confidence score (0.0-1.0)
     * @return Corresponding confidence level enum
     */
    ETypeResolutionConfidence ScoreToConfidenceLevel(float ConfidenceScore);

    /**
     * Converts blueprint type enum to human-readable string
     * @param BlueprintType - The blueprint type to convert
     * @return String representation of the blueprint type
     */
    FString BlueprintTypeToString(EBlueprintType BlueprintType);

    /**
     * Gets resolution statistics for debugging
     * @return Map of resolution statistics
     */
    TMap<FString, float> GetResolutionStatistics();

private:
    // Core resolution methods

    /**
     * Calculates confidence score for a blueprint type based on pattern
     * @param BlueprintType - The blueprint type to score
     * @param PatternType - The detected pattern
     * @param PatternConfidence - Confidence of pattern detection
     * @return Confidence score for the blueprint type
     */
    float CalculatePatternConfidence(EBlueprintType BlueprintType, EBlueprintPatternType PatternType, float PatternConfidence);

    /**
     * Calculates confidence score for a blueprint type based on intent
     * @param BlueprintType - The blueprint type to score
     * @param Intent - The detected intent
     * @return Confidence score for the blueprint type
     */
    float CalculateIntentConfidence(EBlueprintType BlueprintType, const FIntentAnalysisResult& Intent);

    /**
     * Calculates confidence score for a blueprint type based on structure
     * @param BlueprintType - The blueprint type to score
     * @param Structure - The extracted structure
     * @return Confidence score for the blueprint type
     */
    float CalculateStructureConfidence(EBlueprintType BlueprintType, const FExtractedBlueprintStructure& Structure);

    /**
     * Calculates confidence score for a blueprint type based on technical terms
     * @param BlueprintType - The blueprint type to score
     * @param TechnicalTerms - The recognized technical terms
     * @return Confidence score for the blueprint type
     */
    float CalculateTermConfidence(EBlueprintType BlueprintType, const TArray<FTechnicalTerm>& TechnicalTerms);

    // Parent class resolution helpers

    /**
     * Calculates confidence score for a parent class based on components
     * @param ParentClass - The parent class to score
     * @param Components - The detected components
     * @return Confidence score for the parent class
     */
    float CalculateParentClassComponentConfidence(UClass* ParentClass, const TArray<FExtractedComponent>& Components);

    /**
     * Calculates confidence score for a parent class based on functionality
     * @param ParentClass - The parent class to score
     * @param Functions - The detected functions
     * @param Events - The detected events
     * @return Confidence score for the parent class
     */
    float CalculateParentClassFunctionalityConfidence(UClass* ParentClass, const TArray<FExtractedFunction>& Functions, const TArray<FExtractedEvent>& Events);

    // Validation helpers

    /**
     * Checks if a blueprint type supports specific components
     * @param BlueprintType - The blueprint type to check
     * @param Components - The components to check support for
     * @return True if all components are supported
     */
    bool DoesTypeSupportComponents(EBlueprintType BlueprintType, const TArray<FExtractedComponent>& Components);

    /**
     * Checks if a blueprint type supports specific functionality
     * @param BlueprintType - The blueprint type to check
     * @param Functions - The functions to check support for
     * @param Events - The events to check support for
     * @return True if all functionality is supported
     */
    bool DoesTypeSupportFunctionality(EBlueprintType BlueprintType, const TArray<FExtractedFunction>& Functions, const TArray<FExtractedEvent>& Events);

    // Initialization methods

    /**
     * Initializes pattern-to-type mapping rules
     */
    void InitializePatternTypeMapping();

    /**
     * Initializes intent-to-type mapping rules
     */
    void InitializeIntentTypeMapping();

    /**
     * Initializes parent class compatibility rules
     */
    void InitializeParentClassCompatibility();

    /**
     * Initializes component support rules
     */
    void InitializeComponentSupport();

    // Logging and statistics

    /**
     * Logs type resolution activity
     * @param Message - The message to log
     * @param bIsError - Whether this is an error message
     */
    void LogTypeResolution(const FString& Message, bool bIsError = false);

    /**
     * Updates resolution statistics
     * @param ResolvedType - The type that was resolved
     * @param ConfidenceScore - The confidence score of the resolution
     */
    void UpdateResolutionStatistics(EBlueprintType ResolvedType, float ConfidenceScore);

    // Member variables

    /** Pattern to blueprint type mapping with confidence weights */
    TMap<EBlueprintPatternType, TMap<EBlueprintType, float>> PatternTypeMapping;

    /** Intent to blueprint type mapping with confidence weights */
    TMap<EUserIntent, TMap<EBlueprintType, float>> IntentTypeMapping;

    /** Action to blueprint type mapping with confidence weights */
    TMap<EUserAction, TMap<EBlueprintType, float>> ActionTypeMapping;

    /** Blueprint type to compatible parent classes mapping */
    TMap<EBlueprintType, TArray<UClass*>> ParentClassCompatibility;

    /** Blueprint type to supported component types mapping */
    TMap<EBlueprintType, TArray<FString>> ComponentSupport;

    /** Custom type resolution rules */
    TMap<EBlueprintPatternType, TPair<EBlueprintType, float>> CustomTypeRules;

    /** Custom parent class resolution rules */
    TMap<EBlueprintType, TPair<UClass*, float>> CustomParentClassRules;

    /** Confidence level thresholds */
    float VeryLowConfidenceThreshold;
    float LowConfidenceThreshold;
    float MediumConfidenceThreshold;
    float HighConfidenceThreshold;

    /** Resolution statistics for debugging and optimization */
    TMap<EBlueprintType, int32> TypeResolutionCounts;
    TMap<EBlueprintType, float> AverageConfidenceScores;
    TMap<FString, float> ResolutionStatistics;

    /** Whether to enable verbose logging */
    bool bVerboseLogging;

    /** Whether to track resolution statistics */
    bool bTrackStatistics;

    // Blueprint type being resolved
    UPROPERTY(BlueprintReadOnly, Category = "Type Resolution")
    TEnumAsByte<EBlueprintType> BlueprintType;

    // Resolution confidence level
    UPROPERTY(BlueprintReadOnly, Category = "Type Resolution")
    float ConfidenceLevel = 0.0f;

    // Alternative types if resolution is uncertain
    UPROPERTY(BlueprintReadOnly, Category = "Type Resolution")
    TArray<TEnumAsByte<EBlueprintType>> AlternativeTypes;
}; 