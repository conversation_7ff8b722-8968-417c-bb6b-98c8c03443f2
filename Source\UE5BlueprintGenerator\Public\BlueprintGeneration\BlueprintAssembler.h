#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "K2Node.h"
#include "BlueprintGeneration/AssemblyCoordinator.h"
#include "BlueprintGeneration/NodeFactory.h"
#include "BlueprintGeneration/NodeTypeRegistry.h"
#include "BlueprintGeneration/NodePropertyManager.h"
#include "BlueprintGeneration/NodePositionCalculator.h"
#include "BlueprintGeneration/ConnectionManager.h"
#include "NLP/StructureExtractor.h"
#include "BlueprintGeneration/BlueprintGenerationTypes.h"

DECLARE_LOG_CATEGORY_EXTERN(LogBlueprintAssembler, Log, All);

/**
 * Assembly execution mode enumeration
 */
UENUM(BlueprintType)
enum class EAssemblyExecutionMode : uint8
{
    Synchronous,     // Execute assembly synchronously (blocking)
    Asynchronous,    // Execute assembly asynchronously (non-blocking)
    Stepped,         // Execute assembly in steps with user control
    Batch           // Execute multiple assemblies in batch
};

/**
 * Node creation strategy enumeration
 */
UENUM(BlueprintType)
enum class ENodeCreationStrategy : uint8
{
    DependencyOrder, // Create nodes based on dependency order
    TypeGrouped,     // Create nodes grouped by type
    PriorityBased,   // Create nodes based on priority
    Optimized       // Use optimized creation order
};

/**
 * Assembly execution context structure
 */
USTRUCT(BlueprintType)
struct FBLUEPRINTASSEMBLYEXECUTIONCONTEXT
{
    GENERATED_BODY()

    // Current blueprint being assembled
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<UBlueprint> Blueprint = nullptr;

    // Current graph being worked on
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<UEdGraph> CurrentGraph = nullptr;

    // Created nodes during assembly
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TMap<FString, TObjectPtr<UK2Node>> CreatedNodes;

    // Node creation order
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TArray<FString> NodeCreationOrder;

    // Assembly start time
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FDateTime AssemblyStartTime;

    // Current assembly phase
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    EAssemblyPhase CurrentPhase = EAssemblyPhase::None;

    // Assembly configuration
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FAssemblyConfig Config;

    FBLUEPRINTASSEMBLYEXECUTIONCONTEXT()
    {
        Blueprint = nullptr;
        CurrentGraph = nullptr;
        AssemblyStartTime = FDateTime::Now();
        CurrentPhase = EAssemblyPhase::None;
    }
};

/**
 * Node assembly instruction structure
 */
USTRUCT(BlueprintType)
struct FBLUEPRINTASSEMBLYNODEINSTRUCTION
{
    GENERATED_BODY()

    // Unique instruction identifier
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString InstructionId;

    // Node type to create
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString NodeType;

    // Node position
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FVector2D Position = FVector2D::ZeroVector;

    // Node properties to set
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    TMap<FString, FString> Properties;

    // Node connections to establish
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    TArray<FString> Connections;

    // Instruction priority
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    EAssemblyPriority Priority = EAssemblyPriority::Normal;

    // Dependencies (other instruction IDs)
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    TArray<FString> Dependencies;

    // Execution status
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    bool bIsExecuted = false;

    // Error status
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    bool bHasError = false;

    // Error message
    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString ErrorMessage;

    FBLUEPRINTASSEMBLYNODEINSTRUCTION()
    {
        Position = FVector2D::ZeroVector;
        Priority = EAssemblyPriority::Normal;
        bIsExecuted = false;
        bHasError = false;
    }
};

/**
 * Assembly execution plan structure
 */
USTRUCT(BlueprintType)
struct FBLUEPRINTASSEMBLYEXECUTIONPLAN
{
    GENERATED_BODY()

    // Plan identifier
    UPROPERTY(BlueprintReadOnly, Category = "Plan")
    FString PlanId;

    // Assembly instructions
    UPROPERTY(BlueprintReadOnly, Category = "Plan")
    TArray<FNodeAssemblyInstruction> Instructions;

    // Execution order (instruction IDs)
    UPROPERTY(BlueprintReadOnly, Category = "Plan")
    TArray<FString> ExecutionOrder;

    // Estimated execution time
    UPROPERTY(BlueprintReadOnly, Category = "Plan")
    float EstimatedExecutionTime = 0.0f;

    // Plan complexity score
    UPROPERTY(BlueprintReadOnly, Category = "Plan")
    float ComplexityScore = 0.0f;

    // Plan validation status
    UPROPERTY(BlueprintReadOnly, Category = "Plan")
    bool bIsValid = false;

    // Plan validation messages
    UPROPERTY(BlueprintReadOnly, Category = "Plan")
    TArray<FString> ValidationMessages;

    FBLUEPRINTASSEMBLYEXECUTIONPLAN()
    {
        EstimatedExecutionTime = 0.0f;
        ComplexityScore = 0.0f;
        bIsValid = false;
    }
};

/**
 * Assembly execution result structure
 */
USTRUCT(BlueprintType)
struct FBLUEPRINTASSEMBLYEXECUTIONRESULT
{
    GENERATED_BODY()

    // Execution success status
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    // Executed instructions count
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 ExecutedInstructions = 0;

    // Failed instructions count
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 FailedInstructions = 0;

    // Actual execution time
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float ActualExecutionTime = 0.0f;

    // Created nodes
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TMap<FString, TObjectPtr<UK2Node>> CreatedNodes;

    // Execution errors
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> ExecutionErrors;

    // Execution warnings
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> ExecutionWarnings;

    // Performance metrics
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TMap<FString, float> PerformanceMetrics;

    FBLUEPRINTASSEMBLYEXECUTIONRESULT()
    {
        bSuccess = false;
        ExecutedInstructions = 0;
        FailedInstructions = 0;
        ActualExecutionTime = 0.0f;
    }
};

/**
 * Assembly batch configuration structure
 */
USTRUCT(BlueprintType)
struct FASSEMBLYBATCHCONFIG
{
    GENERATED_BODY()

    // Maximum concurrent assemblies
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch", meta = (ClampMin = "1", ClampMax = "8"))
    int32 MaxConcurrentAssemblies = 2;

    // Batch timeout (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch", meta = (ClampMin = "10", ClampMax = "600"))
    float BatchTimeout = 120.0f;

    // Enable batch optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    bool bEnableBatchOptimization = true;

    // Enable progress reporting
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    bool bEnableProgressReporting = true;

    // Stop on first error
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    bool bStopOnFirstError = false;

    FASSEMBLYBATCHCONFIG()
    {
        MaxConcurrentAssemblies = 2;
        BatchTimeout = 120.0f;
        bEnableBatchOptimization = true;
        bEnableProgressReporting = true;
        bStopOnFirstError = false;
    }
};

/**
 * Delegate declarations for assembly events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssemblyExecutionStarted, const FString&, PlanId);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAssemblyInstructionExecuted, const FString&, InstructionId, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssemblyExecutionCompleted, const FBLUEPRINTASSEMBLYEXECUTIONRESULT&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAssemblyExecutionError, const FString&, ErrorMessage, const FString&, InstructionId);

/**
 * Blueprint Assembler - Core blueprint assembly engine
 * 
 * This class executes the blueprint assembly pipeline by creating nodes,
 * configuring properties, calculating positions, and establishing connections
 * based on the assembly plan generated from natural language analysis.
 */
class UE5BLUEPRINTGENERATOR_API FBlueprintAssembler
{
public:
    FBlueprintAssembler();
    virtual ~FBlueprintAssembler();

    // Core assembly operations
    bool ExecuteAssembly(const FAssemblyRequest& Request, FBLUEPRINTASSEMBLYEXECUTIONRESULT& OutResult);
    bool ExecuteAssemblyPlan(const FBLUEPRINTASSEMBLYEXECUTIONPLAN& Plan, const FBLUEPRINTASSEMBLYEXECUTIONCONTEXT& Context, FBLUEPRINTASSEMBLYEXECUTIONRESULT& OutResult);

    // Assembly planning
    bool CreateAssemblyPlan(const FExtractedBlueprintStructure& Structure, const FDependencyAnalysisResult& Dependencies, FBLUEPRINTASSEMBLYEXECUTIONPLAN& OutPlan);
    bool ValidateAssemblyPlan(const FBLUEPRINTASSEMBLYEXECUTIONPLAN& Plan);
    bool OptimizeAssemblyPlan(FBLUEPRINTASSEMBLYEXECUTIONPLAN& Plan);

    // Execution control
    bool StartAsyncAssembly(const FAssemblyRequest& Request);
    bool PauseAssembly();
    bool ResumeAssembly();
    bool CancelAssembly();
    bool IsAssemblyInProgress() const;

    // Batch operations
    bool ExecuteBatchAssembly(const TArray<FAssemblyRequest>& Requests, const FAssemblyBatchConfig& BatchConfig, TArray<FBLUEPRINTASSEMBLYEXECUTIONRESULT>& OutResults);

    // Configuration
    void SetExecutionMode(EAssemblyExecutionMode Mode);
    EAssemblyExecutionMode GetExecutionMode() const;
    void SetNodeCreationStrategy(ENodeCreationStrategy Strategy);
    ENodeCreationStrategy GetNodeCreationStrategy() const;

    // Progress monitoring
    float GetAssemblyProgress() const;
    FBLUEPRINTASSEMBLYEXECUTIONCONTEXT GetCurrentContext() const;
    TArray<FNodeAssemblyInstruction> GetCurrentInstructions() const;

    // Event delegates
    FOnAssemblyExecutionStarted OnAssemblyExecutionStarted;
    FOnAssemblyInstructionExecuted OnAssemblyInstructionExecuted;
    FOnAssemblyExecutionCompleted OnAssemblyExecutionCompleted;
    FOnAssemblyExecutionError OnAssemblyExecutionError;

private:
    // Component references
    TSharedPtr<FNodeFactory> NodeFactory;
    TSharedPtr<FNodeTypeRegistry> NodeTypeRegistry;
    TSharedPtr<FNodePropertyManager> PropertyManager;
    TSharedPtr<FNodePositionCalculator> PositionCalculator;
    TSharedPtr<FConnectionManager> ConnectionManager;

    // Execution state
    EAssemblyExecutionMode ExecutionMode;
    ENodeCreationStrategy NodeCreationStrategy;
    bool bIsAssemblyInProgress;
    bool bIsAssemblyPaused;
    FBLUEPRINTASSEMBLYEXECUTIONCONTEXT CurrentContext;
    FBLUEPRINTASSEMBLYEXECUTIONPLAN CurrentPlan;
    FDateTime ExecutionStartTime;

    // Assembly planning methods
    bool CreateVariableInstructions(const TArray<FExtractedVariable>& Variables, TArray<FNodeAssemblyInstruction>& OutInstructions);
    bool CreateFunctionInstructions(const TArray<FExtractedFunction>& Functions, TArray<FNodeAssemblyInstruction>& OutInstructions);
    bool CreateEventInstructions(const TArray<FExtractedEvent>& Events, TArray<FNodeAssemblyInstruction>& OutInstructions);
    bool CreateComponentInstructions(const TArray<FExtractedComponent>& Components, TArray<FNodeAssemblyInstruction>& OutInstructions);

    // Execution order optimization
    bool OptimizeExecutionOrder(FBLUEPRINTASSEMBLYEXECUTIONPLAN& Plan);
    bool ResolveDependencyOrder(TArray<FNodeAssemblyInstruction>& Instructions, TArray<FString>& OutExecutionOrder);
    bool ValidateExecutionOrder(const TArray<FString>& ExecutionOrder, const TArray<FNodeAssemblyInstruction>& Instructions);

    // Instruction execution
    bool ExecuteInstruction(const FNodeAssemblyInstruction& Instruction, FBLUEPRINTASSEMBLYEXECUTIONCONTEXT& Context);
    bool CreateNodeFromInstruction(const FNodeAssemblyInstruction& Instruction, FBLUEPRINTASSEMBLYEXECUTIONCONTEXT& Context, TObjectPtr<UK2Node>& OutNode);
    bool ConfigureNodeProperties(const FNodeAssemblyInstruction& Instruction, TObjectPtr<UK2Node> Node);
    bool SetNodePosition(const FNodeAssemblyInstruction& Instruction, TObjectPtr<UK2Node> Node);

    // Connection establishment
    bool EstablishConnections(const FBLUEPRINTASSEMBLYEXECUTIONPLAN& Plan, FBLUEPRINTASSEMBLYEXECUTIONCONTEXT& Context);
    bool ParseConnectionString(const FString& ConnectionString, FString& OutSourceNode, FString& OutSourcePin, FString& OutTargetNode, FString& OutTargetPin);

    // Error handling
    bool HandleExecutionError(const FString& ErrorMessage, const FString& InstructionId);
    bool AttemptInstructionRecovery(const FNodeAssemblyInstruction& Instruction, FBLUEPRINTASSEMBLYEXECUTIONCONTEXT& Context);

    // Performance monitoring
    void StartExecutionTimer();
    void StopExecutionTimer();
    void UpdatePerformanceMetrics(FBLUEPRINTASSEMBLYEXECUTIONRESULT& Result);

    // Validation helpers
    bool ValidateExecutionContext(const FBLUEPRINTASSEMBLYEXECUTIONCONTEXT& Context);
    bool ValidateInstruction(const FNodeAssemblyInstruction& Instruction);

    // Utility methods
    FString GenerateInstructionId(const FString& NodeType, int32 Index);
    FString GeneratePlanId();
    void LogExecutionProgress(const FString& Message);
    void BroadcastExecutionStarted(const FString& PlanId);
    void BroadcastInstructionExecuted(const FString& InstructionId, bool bSuccess);
    void BroadcastExecutionCompleted(const FBLUEPRINTASSEMBLYEXECUTIONRESULT& Result);
    void BroadcastExecutionError(const FString& ErrorMessage, const FString& InstructionId);

    // Component initialization
    void InitializeComponents();
    void ShutdownComponents();

    // Async execution support
    void ExecuteAsyncAssembly();
    bool bAsyncExecutionRequested;
    FAssemblyRequest PendingAsyncRequest;
}; 